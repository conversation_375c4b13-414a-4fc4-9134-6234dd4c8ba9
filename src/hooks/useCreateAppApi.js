import { useState, useCallback } from "react";
import { trackCustomEvent } from "../utils/analytics";
import { API_ENDPOINT } from "../utils/consts";
import { useAppStorage } from "./useAppStorage";
import { calculateMonthsInBusiness } from "../utils/dateUtils";

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the createApp API call
 * @returns {Object} API state and methods
 */
export function useCreateAppApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);
  const { utmParams } = useAppStorage();

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Create a new application
   * @param {Object} formData - Form data to submit
   * @returns {Promise<Object>} API response
   */
  const createApp = useCallback(
    async (formData) => {
      let result_status = "unknown";
      let error_message = "";
      setStatus(STATUS.LOADING);
      setError({ message: null, id: null });
      setResult(null);

      let result = null;
      try {
        // Prepare the request body
        const requestBody = {
          preQualifyFields: formData,
        };

        if (Object.keys(utmParams).length > 0) {
          requestBody.utm = utmParams;
        }

        // Call the API directly using fetch
        const response = await fetch(`${API_ENDPOINT}/app`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        result = await response.json();

        if (!response.ok) {
          throw result;
        } else {
          result_status = "success";

          // Add the form data to the response for convenience
          result.preQualifyFields = formData;

          // Set the result
          setResult(result);
          setStatus(STATUS.SUCCESS);
        }

        // Return the response
        return result;
      } catch (error) {
        result_status = "error";
        error_message =
          error?.error ||
          error?.message ||
          "There was a problem creating your application. Please try again later.";
        const id = error?.errorId || "UNKNOWN_ERROR";
        setError({ message: error_message, id });
        setStatus(STATUS.ERROR);

        // Re-throw the error for the caller to handle
        throw error;
      } finally {
        const startedAt = Number(
          sessionStorage.getItem("prequal_form_started_at")
        );
        const submittedAt = Number(
          sessionStorage.getItem("prequal_form_submitted_at")
        );
        const duration = Math.round((submittedAt - startedAt) / 1000) || null;

        const {
          fundingAmount,
          purpose,
          topPriority,
          timeline,
          monthlyRevenue,
          estimatedFICO,
          businessStartDate,
        } = formData;

        const eventParams = {
          fundingAmount,
          purpose,
          topPriority,
          timeline,
          monthlyRevenue,
          estimatedFICO,
          months_in_business: calculateMonthsInBusiness(businessStartDate),
          form_duration: duration,
          approval_status:
            result.status === "PREQUAL_APPROVED" ? "Approved" : "Denied",
          approval_amount: result.approvalAmount || null,
          assigned_agent: result.agent?.name ?? null,
          denial_reason: result.reason ? result.reason : null,
        };

        trackCustomEvent("prequal_form_submitted", eventParams, false);
      }
    },
    [utmParams]
  );

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    createApp,
    reset,
  };
}
