import { useEffect, useRef, useCallback } from "react";

/**
 * Custom hook to detect user idle state
 * @param {Function} onIdle - Callback function to execute when user becomes idle
 * @param {number} idleTime - Time in milliseconds to wait before considering user idle (default: 2000ms)
 * @param {boolean} enabled - Whether idle detection is enabled (default: true)
 * @returns {Object} Methods to control idle detection
 */
export function useIdleDetection(onIdle, idleTime = 2000, enabled = true) {
  const timeoutRef = useRef(null);
  const onIdleRef = useRef(onIdle);

  // Update the callback ref when onIdle changes
  useEffect(() => {
    onIdleRef.current = onIdle;
  }, [onIdle]);

  /**
   * Reset the idle timer
   */
  const resetTimer = useCallback(() => {
    if (!enabled) return;

    // Clear existing timer
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timer
    timeoutRef.current = setTimeout(() => {
      if (onIdleRef.current) {
        onIdleRef.current();
      }
    }, idleTime);
  }, [idleTime, enabled]);

  /**
   * Clear the idle timer
   */
  const clearTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  /**
   * Start idle detection
   */
  const startDetection = useCallback(() => {
    resetTimer();
  }, [resetTimer]);

  /**
   * Stop idle detection
   */
  const stopDetection = useCallback(() => {
    clearTimer();
  }, [clearTimer]);

  // Set up event listeners for user activity
  useEffect(() => {
    if (!enabled) return;

    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, resetTimer, true);
    });

    // Start the initial timer
    resetTimer();

    // Cleanup function
    return () => {
      // Remove event listeners
      events.forEach(event => {
        document.removeEventListener(event, resetTimer, true);
      });
      
      // Clear timer
      clearTimer();
    };
  }, [resetTimer, clearTimer, enabled]);

  return {
    startDetection,
    stopDetection,
    resetTimer,
    clearTimer,
  };
}
