import { useEffect } from "react";
import { PreQualificationForm } from "../components/PreQualificationForm/PreQualificationForm";
import { trackClick } from "../utils/analytics.js";

const TrustPilotWidget = () => {
  useEffect(() => {
    // Trustpilot widget script
    const script = document.createElement("script");
    script.src =
      "//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="bg-gray-50 h-27 p-2 rounded-sm shadow-sm w-fit">
      <div
        className="trustpilot-widget"
        data-locale="en-US"
        data-template-id="53aa8807dec7e10d38f59f32"
        data-businessunit-id="66d0b7dd321310cee73cdf4b"
        data-style-height=""
        data-style-width="180px"
      >
        <a
          href="https://www.trustpilot.com/review/pinnacleconsultingny.com"
          target="_blank"
          rel="noopener"
        >
          Trustpilot
        </a>
      </div>
    </div>
  );
};

const PreQualifyExplainer = () => {
  return (
    <div className="w-full p-6">
      <div className="mb-6 sm:mb-8">
        <h2 className="text-xl sm:text-2xl font-bold text-blue-900 mb-3 sm:mb-4">
          First step, let's figure out your funding.
        </h2>
      </div>

      <div className="mb-10">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">
          Our promise to you
        </h3>
        <ul className="space-y-3 sm:space-y-4">
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">
                We Guide, You Grow
              </p>
              <p className="text-xs sm:text-sm text-gray-600">
                Your funding expert will be with you every step of the way
              </p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">
                Zero Credit Impact
              </p>
              <p className="text-xs sm:text-sm text-gray-600">
                We don't ever do a hard credit check
              </p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="mr-2 text-green-600 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-800 text-sm sm:text-base">
                Your data is protected
              </p>
              <p className="text-xs sm:text-sm text-gray-600" data-hj-allow>
                We use 256 bit encryption to guard your data
              </p>
            </div>
          </li>
        </ul>
      </div>
      <TrustPilotWidget />
    </div>
  );
};

const QuickLinksSection = () => {
  const handlePhoneClick = () => {
    trackClick("Phone", { phone: "(*************" });
  };

  const handleFAQClick = () => {
    trackClick("FAQ", {
      destination: "https://pinnaclefundingco.com/faq/",
    });
  };

  const handleWebsiteClick = () => {
    trackClick("Website", {
      destination: "https://pinnacleconsultingny.com/",
    });
  };
  return (
    <div className="mt-12">
      <h2 className="text-4xl font-bold text-blue-900 mb-10 text-center">
        We’re here to help!
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Need personalized support?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Connect with a funding specialist
          </p>
          <a
            href="tel:3476948180"
            className="flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200 text-base sm:text-lg"
            onClick={handlePhoneClick}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
            (*************
          </a>
        </div>

        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Have more questions?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Explore our comprehensive{" "}
            <a
              href="https://pinnaclefundingco.com/faq/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200"
              onClick={handleFAQClick}
            >
              FAQ
            </a>{" "}
            for quick answers and helpful insights.
          </p>
          <div />
        </div>

        <div className="card bg-white p-4 sm:p-6 rounded-sm shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-2 h-10">
            Want to learn more?
          </h3>
          <p className="text-gray-600 mb-3 sm:mb-4">
            Discover how we work and what we offer on our{" "}
            <a
              href="https://pinnacleconsultingny.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 font-medium hover:text-blue-800 transition-colors duration-200"
              onClick={handleWebsiteClick}
            >
              Website
            </a>
            .
          </p>
          <div />
        </div>
      </div>
    </div>
  );
};

export const PreQualifyPage = ({ preQualifyParams }) => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
        <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
          <PreQualifyExplainer />
        </div>
        <div className="w-full lg:w-2/3">
          <PreQualificationForm preQualifyParams={preQualifyParams} />
        </div>
      </div>
      <QuickLinksSection />
    </div>
  );
};
