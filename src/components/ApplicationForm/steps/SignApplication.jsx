import React, { useEffect, useState, useCallback } from "react";
import { logger } from "../../../utils/logger";
import { usePandaDocStatusApi } from "../../../hooks/usePandaDocStatusApi";
import { useIdleDetection } from "../../../hooks/useIdleDetection";

/**
 * Sign Application step component
 * This component displays a PandaDoc iframe for document signing
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onDocumentSigned - Function to call when document is signed
 * @returns {JSX.Element}
 */
export const SignApplication = ({
  onDocumentSigned,
  sessionId,
  onDocumentLoaded,
  onRevisionsClick,
  appId,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const { checkDocumentStatus } = usePandaDocStatusApi();

  const handleIdle = useCallback(async () => {
    if (!appId || !sessionId) return;

    try {
      const statusResult = await checkDocumentStatus(appId);

      if (statusResult?.signed === true) {
        logger.log(
          "Document signed detected via idle check, proceeding to next step"
        );
        onDocumentSigned();
      }
    } catch (error) {
      logger.error("Error checking document status during idle:", error);
    }
  }, [appId, sessionId, checkDocumentStatus, onDocumentSigned]);

  const { startDetection, stopDetection } = useIdleDetection(
    handleIdle,
    2000,
    !!appId && !!sessionId
  );

  useEffect(() => {
    // Set up event listener for PandaDoc events
    const handlePandaDocEvent = (event) => {
      const { data } = event;
      if (!data) return;

      const { type, payload } = data;

      switch (type) {
        case "session_view.document.loaded":
          setIsLoading(false);
          onDocumentLoaded();
          startDetection();
          break;

        case "session_view.document.completed":
          stopDetection();
          onDocumentSigned();
          break;

        case "session_view.document.exception":
          logger.error("Exception during document finalization", payload);
          setError("There was a problem with the document. Please try again.");
          break;

        default:
          break;
      }
    };

    window.addEventListener("message", handlePandaDocEvent);

    // Clean up event listener
    return () => {
      window.removeEventListener("message", handlePandaDocEvent);
      stopDetection();
    };
  }, [
    sessionId,
    onDocumentSigned,
    onDocumentLoaded,
    startDetection,
    stopDetection,
  ]);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Sign Your Application</h3>

      <p className="text-gray-600 mb-6">
        Please review and sign your application. For revisions{" "}
        <a
          onClick={() => {
            if (isLoading || !sessionId) return;
            onRevisionsClick();
          }}
          className="text-blue-600 hover:underline cursor-pointer"
        >
          click here
        </a>
        .
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}

        {/* PandaDoc iframe */}
        <div className="border border-gray-300 rounded-sm">
          <iframe
            src={`https://app.pandadoc.com/s/${sessionId}`}
            width="100%"
            height="800px"
            title="Sign Application"
            className="rounded-sm"
          ></iframe>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        <p>
          By signing this document, you agree to the terms and conditions of the
          funding application.
        </p>
      </div>
    </div>
  );
};
